using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容删除设置窗体
    /// </summary>
    public partial class ContentDeletionForm : Form
    {
        // 当前设置
        private ContentDeletionSettings _currentSettings;

        // 各标签页的控件容器
        private readonly Dictionary<string, Panel> _tabPanels = new Dictionary<string, Panel>();

        public ContentDeletionForm()
        {
            InitializeComponent();
            InitializeSettings();
            InitializeTabPages();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化设置
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                _currentSettings = config.ContentDeletionSettings;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载内容删除设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _currentSettings = new ContentDeletionSettings();
            }
        }

        /// <summary>
        /// 初始化标签页
        /// </summary>
        private void InitializeTabPages()
        {
            try
            {
                // 初始化删除文档标签页
                InitializeDocumentDeletionTab();

                // 初始化内容删除设置标签页
                InitializeContentRemovalTab();

                // 初始化文本删除标签页
                InitializeTextDeletionTab();

                // 初始化图片删除标签页
                InitializeImageDeletionTab();

                // 初始化表格删除标签页
                InitializeTableDeletionTab();

                // 初始化图表删除标签页
                InitializeChartDeletionTab();

                // 初始化音频视频删除标签页
                InitializeMediaDeletionTab();

                // 初始化联系方式删除标签页
                InitializeContactDeletionTab();

                // 初始化动画删除标签页
                InitializeAnimationDeletionTab();

                // 初始化备注删除标签页
                InitializeNotesDeletionTab();

                // 初始化格式删除标签页
                InitializeFormatDeletionTab();

                // 加载当前设置到界面
                LoadSettingsToUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化标签页失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化删除文档标签页
        /// </summary>
        private void InitializeDocumentDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageDocumentDeletion.Controls.Add(panel);
            _tabPanels["DocumentDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用删除文档功能", 10, yPos);
            chkMasterSwitch.Name = "chkDocumentDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 分组框：文件名长度检查
            var grpFileNameLength = CreateGroupBox("文件名长度检查", 10, yPos, 720, 90);
            var chkFileNameLength = CreateCheckBox("启用文件名长度检查", 15, 30);
            chkFileNameLength.Name = "chkFileNameLengthCheck";
            var lblFileNameMin = CreateLabel("最小长度:", 200, 30);
            var numFileNameMin = CreateNumericUpDown(1, 1000, 280, 27);
            numFileNameMin.Name = "numFileNameMinLength";
            var lblFileNameMax = CreateLabel("最大长度:", 380, 30);
            var numFileNameMax = CreateNumericUpDown(1, 1000, 460, 27);
            numFileNameMax.Name = "numFileNameMaxLength";
            var lblFileNameUnit = CreateLabel("字符", 540, 30);

            grpFileNameLength.Controls.AddRange(new Control[] {
                chkFileNameLength, lblFileNameMin, numFileNameMin,
                lblFileNameMax, numFileNameMax, lblFileNameUnit
            });
            panel.Controls.Add(grpFileNameLength);
            yPos += 100;

            // 分组框：文件大小检查
            var grpFileSize = CreateGroupBox("文件大小检查", 10, yPos, 720, 90);
            var chkFileSize = CreateCheckBox("启用文件大小检查", 15, 30);
            chkFileSize.Name = "chkFileSizeCheck";
            var lblFileSizeMin = CreateLabel("最小大小:", 200, 30);
            var numFileSizeMin = CreateNumericUpDown(1, 999999, 280, 27);
            numFileSizeMin.Name = "numFileSizeMin";
            var lblFileSizeMax = CreateLabel("最大大小:", 380, 30);
            var numFileSizeMax = CreateNumericUpDown(1, 999999, 460, 27);
            numFileSizeMax.Name = "numFileSizeMax";
            var cmbFileSizeUnit = CreateComboBox(new[] { "B", "KB", "MB" }, 540, 27);
            cmbFileSizeUnit.Name = "cmbFileSizeUnit";

            grpFileSize.Controls.AddRange(new Control[] {
                chkFileSize, lblFileSizeMin, numFileSizeMin,
                lblFileSizeMax, numFileSizeMax, cmbFileSizeUnit
            });
            panel.Controls.Add(grpFileSize);
            yPos += 100;

            // 分组框：内容字符数检查
            var grpContentCharCount = CreateGroupBox("内容字符数检查", 10, yPos, 720, 90);
            var chkContentCharCount = CreateCheckBox("启用内容字符数检查", 15, 30);
            chkContentCharCount.Name = "chkContentCharCountCheck";
            var lblContentCharMin = CreateLabel("最小字符数:", 200, 30);
            var numContentCharMin = CreateNumericUpDown(1, 999999, 300, 27);
            numContentCharMin.Name = "numContentCharMin";
            var lblContentCharMax = CreateLabel("最大字符数:", 420, 30);
            var numContentCharMax = CreateNumericUpDown(1, 999999, 520, 27);
            numContentCharMax.Name = "numContentCharMax";

            grpContentCharCount.Controls.AddRange(new Control[] {
                chkContentCharCount, lblContentCharMin, numContentCharMin,
                lblContentCharMax, numContentCharMax
            });
            panel.Controls.Add(grpContentCharCount);
            yPos += 100;

            // 分组框：页数检查
            var grpPageCount = CreateGroupBox("页数检查", 10, yPos, 720, 90);
            var chkPageCount = CreateCheckBox("启用页数检查", 15, 30);
            chkPageCount.Name = "chkPageCountCheck";
            var lblPageMin = CreateLabel("最小页数:", 200, 30);
            var numPageMin = CreateNumericUpDown(1, 9999, 280, 27);
            numPageMin.Name = "numPageMin";
            var lblPageMax = CreateLabel("最大页数:", 380, 30);
            var numPageMax = CreateNumericUpDown(1, 9999, 460, 27);
            numPageMax.Name = "numPageMax";
            var lblPageUnit = CreateLabel("页", 540, 30);

            grpPageCount.Controls.AddRange(new Control[] {
                chkPageCount, lblPageMin, numPageMin,
                lblPageMax, numPageMax, lblPageUnit
            });
            panel.Controls.Add(grpPageCount);
            yPos += 100;

            // 分组框：文件名非法词检查
            var grpFileNameIllegal = CreateGroupBox("文件名非法词检查", 10, yPos, 720, 90);
            var chkFileNameIllegal = CreateCheckBox("启用文件名非法词检查", 15, 30);
            chkFileNameIllegal.Name = "chkFileNameIllegalCheck";
            var lblFileNameIllegal = CreateLabel("非法词数量:", 200, 30);
            var lblFileNameIllegalCount = CreateLabel("0个", 280, 30);
            lblFileNameIllegalCount.Name = "lblFileNameIllegalCount";
            lblFileNameIllegalCount.ForeColor = Color.Gray;
            var btnFileNameIllegal = CreateButton("设置非法词", 400, 27, 100, 30);
            btnFileNameIllegal.Name = "btnEditFileNameIllegalWords";

            grpFileNameIllegal.Controls.AddRange(new Control[] {
                chkFileNameIllegal, lblFileNameIllegal, lblFileNameIllegalCount, btnFileNameIllegal
            });
            panel.Controls.Add(grpFileNameIllegal);
            yPos += 100;

            // 分组框：内容非法词检查
            var grpContentIllegal = CreateGroupBox("内容非法词检查", 10, yPos, 720, 90);
            var chkContentIllegal = CreateCheckBox("启用内容非法词检查", 15, 30);
            chkContentIllegal.Name = "chkContentIllegalCheck";
            var lblContentIllegal = CreateLabel("非法词数量:", 200, 30);
            var lblContentIllegalCount = CreateLabel("0个", 280, 30);
            lblContentIllegalCount.Name = "lblContentIllegalCount";
            lblContentIllegalCount.ForeColor = Color.Gray;
            var btnContentIllegal = CreateButton("设置非法词", 400, 27, 100, 30);
            btnContentIllegal.Name = "btnEditContentIllegalWords";

            grpContentIllegal.Controls.AddRange(new Control[] {
                chkContentIllegal, lblContentIllegal, lblContentIllegalCount, btnContentIllegal
            });
            panel.Controls.Add(grpContentIllegal);
        }

        /// <summary>
        /// 初始化内容删除设置标签页
        /// </summary>
        private void InitializeContentRemovalTab()
        {
            var panel = CreateScrollablePanel();
            tabPageContentRemoval.Controls.Add(panel);
            _tabPanels["ContentRemoval"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用内容删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkContentRemovalMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 幻灯片删除选项
            var grpSlideOptions = CreateGroupBox("幻灯片删除选项", 10, yPos, 720, 180);

            var chkBlankSlides = CreateCheckBox("删除空白幻灯片", 15, 25);
            chkBlankSlides.Name = "chkDeleteBlankSlides";

            var chkFirstSlide = CreateCheckBox("删除第一页", 15, 50);
            chkFirstSlide.Name = "chkDeleteFirstSlide";

            var chkLastSlide = CreateCheckBox("删除最后一页", 15, 75);
            chkLastSlide.Name = "chkDeleteLastSlide";

            var chkSlideRange = CreateCheckBox("删除指定页范围", 15, 100);
            chkSlideRange.Name = "chkDeleteSlideRange";
            var lblSlideRangeFrom = CreateLabel("从第", 200, 100);
            var numSlideRangeStart = CreateNumericUpDown(1, 9999, 240, 97);
            numSlideRangeStart.Name = "numSlideRangeStart";
            var lblSlideRangeTo = CreateLabel("页到第", 320, 100);
            var numSlideRangeEnd = CreateNumericUpDown(1, 9999, 370, 97);
            numSlideRangeEnd.Name = "numSlideRangeEnd";
            var lblSlideRangeEnd = CreateLabel("页", 450, 100);

            var chkKeywordSlides = CreateCheckBox("删除包含关键词的页", 15, 125);
            chkKeywordSlides.Name = "chkDeleteKeywordSlides";
            var txtSlideKeywords = CreateTextBox(200, 125, 480, 20);
            txtSlideKeywords.Name = "txtSlideKeywords";
            txtSlideKeywords.PlaceholderText = "输入关键词，用逗号分隔";

            grpSlideOptions.Controls.AddRange(new Control[] {
                chkBlankSlides, chkFirstSlide, chkLastSlide, chkSlideRange,
                lblSlideRangeFrom, numSlideRangeStart, lblSlideRangeTo, numSlideRangeEnd, lblSlideRangeEnd,
                chkKeywordSlides, txtSlideKeywords
            });
            panel.Controls.Add(grpSlideOptions);
            yPos += 190;

            // 段落和行删除选项
            var grpParagraphOptions = CreateGroupBox("段落和行删除选项", 10, yPos, 720, 80);

            var chkBlankParagraphs = CreateCheckBox("删除空白段落", 15, 25);
            chkBlankParagraphs.Name = "chkDeleteBlankParagraphs";

            var chkBlankLines = CreateCheckBox("删除空白行", 15, 50);
            chkBlankLines.Name = "chkDeleteBlankLines";

            grpParagraphOptions.Controls.AddRange(new Control[] {
                chkBlankParagraphs, chkBlankLines
            });
            panel.Controls.Add(grpParagraphOptions);
        }

        /// <summary>
        /// 初始化文本删除标签页
        /// </summary>
        private void InitializeTextDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageTextDeletion.Controls.Add(panel);
            _tabPanels["TextDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用文本删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkTextDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 段落删除
            var grpParagraphDeletion = CreateGroupBox("删除包含指定文本的段落", 10, yPos, 720, 100);
            var chkParagraphDeletion = CreateCheckBox("启用段落删除", 15, 25);
            chkParagraphDeletion.Name = "chkDeleteParagraphsWithText";
            var lblParagraphKeywords = CreateLabel("关键词列表（一行一个）:", 15, 50);
            var txtParagraphKeywords = CreateTextBox(15, 70, 680, 20, true);
            txtParagraphKeywords.Name = "txtParagraphKeywords";

            grpParagraphDeletion.Controls.AddRange(new Control[] {
                chkParagraphDeletion, lblParagraphKeywords, txtParagraphKeywords
            });
            panel.Controls.Add(grpParagraphDeletion);
            yPos += 110;

            // 文本框删除
            var grpTextBoxDeletion = CreateGroupBox("删除包含指定文本的文本框", 10, yPos, 720, 100);
            var chkTextBoxDeletion = CreateCheckBox("启用文本框删除", 15, 25);
            chkTextBoxDeletion.Name = "chkDeleteTextBoxesWithText";
            var lblTextBoxKeywords = CreateLabel("关键词列表（一行一个）:", 15, 50);
            var txtTextBoxKeywords = CreateTextBox(15, 70, 680, 20, true);
            txtTextBoxKeywords.Name = "txtTextBoxKeywords";

            grpTextBoxDeletion.Controls.AddRange(new Control[] {
                chkTextBoxDeletion, lblTextBoxKeywords, txtTextBoxKeywords
            });
            panel.Controls.Add(grpTextBoxDeletion);
            yPos += 110;

            // 表格删除
            var grpTableTextDeletion = CreateGroupBox("删除包含指定文本的表格", 10, yPos, 720, 100);
            var chkTableTextDeletion = CreateCheckBox("启用表格删除", 15, 25);
            chkTableTextDeletion.Name = "chkDeleteTablesWithText";
            var lblTableKeywords = CreateLabel("关键词列表（一行一个）:", 15, 50);
            var txtTableKeywords = CreateTextBox(15, 70, 680, 20, true);
            txtTableKeywords.Name = "txtTableKeywords";

            grpTableTextDeletion.Controls.AddRange(new Control[] {
                chkTableTextDeletion, lblTableKeywords, txtTableKeywords
            });
            panel.Controls.Add(grpTableTextDeletion);
        }

        /// <summary>
        /// 初始化图片删除标签页
        /// </summary>
        private void InitializeImageDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageImageDeletion.Controls.Add(panel);
            _tabPanels["ImageDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用图片删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkImageDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 基本图片删除选项
            var grpBasicImageOptions = CreateGroupBox("基本图片删除选项", 10, yPos, 720, 130);

            var chkAllImages = CreateCheckBox("删除所有图片", 15, 25);
            chkAllImages.Name = "chkDeleteAllImages";

            var chkSpecificImages = CreateCheckBox("删除指定图片", 15, 50);
            chkSpecificImages.Name = "chkDeleteSpecificImages";
            var txtSpecificImages = CreateTextBox(200, 50, 480, 20);
            txtSpecificImages.Name = "txtSpecificImageNames";
            txtSpecificImages.PlaceholderText = "输入图片名称，用逗号分隔";

            var chkLastSlideImages = CreateCheckBox("删除最后一页图片", 15, 75);
            chkLastSlideImages.Name = "chkDeleteLastSlideImages";

            var chkBackgroundImages = CreateCheckBox("删除背景图片", 15, 100);
            chkBackgroundImages.Name = "chkDeleteBackgroundImages";

            grpBasicImageOptions.Controls.AddRange(new Control[] {
                chkAllImages, chkSpecificImages, txtSpecificImages,
                chkLastSlideImages, chkBackgroundImages
            });
            panel.Controls.Add(grpBasicImageOptions);
            yPos += 140;

            // 页范围图片删除
            var grpImageRangeOptions = CreateGroupBox("页范围图片删除", 10, yPos, 720, 80);

            var chkImageRange = CreateCheckBox("删除指定页范围的图片", 15, 25);
            chkImageRange.Name = "chkDeleteImageRange";
            var lblImageRangeFrom = CreateLabel("从第", 250, 25);
            var numImageRangeStart = CreateNumericUpDown(1, 9999, 290, 22);
            numImageRangeStart.Name = "numImageRangeStart";
            var lblImageRangeTo = CreateLabel("页到第", 370, 25);
            var numImageRangeEnd = CreateNumericUpDown(1, 9999, 420, 22);
            numImageRangeEnd.Name = "numImageRangeEnd";
            var lblImageRangeEndText = CreateLabel("页", 500, 25);

            grpImageRangeOptions.Controls.AddRange(new Control[] {
                chkImageRange, lblImageRangeFrom, numImageRangeStart,
                lblImageRangeTo, numImageRangeEnd, lblImageRangeEndText
            });
            panel.Controls.Add(grpImageRangeOptions);
            yPos += 90;

            // 位置条件图片删除
            var grpPositionImageOptions = CreateGroupBox("按位置删除图片", 10, yPos, 720, 110);

            var chkPositionImages = CreateCheckBox("删除指定位置范围内的图片", 15, 30);
            chkPositionImages.Name = "chkDeletePositionImages";

            var lblRectX = CreateLabel("左边距:", 15, 55);
            var numRectX = CreateNumericUpDown(0, 9999, 80, 52);
            numRectX.Name = "numPositionX";
            numRectX.DecimalPlaces = 1;

            var lblRectY = CreateLabel("上边距:", 180, 55);
            var numRectY = CreateNumericUpDown(0, 9999, 240, 52);
            numRectY.Name = "numPositionY";
            numRectY.DecimalPlaces = 1;

            var lblRectWidth = CreateLabel("宽度:", 340, 55);
            var numRectWidth = CreateNumericUpDown(1, 9999, 390, 52);
            numRectWidth.Name = "numPositionWidth";
            numRectWidth.DecimalPlaces = 1;

            var lblRectHeight = CreateLabel("高度:", 490, 55);
            var numRectHeight = CreateNumericUpDown(1, 9999, 540, 52);
            numRectHeight.Name = "numPositionHeight";
            numRectHeight.DecimalPlaces = 1;

            grpPositionImageOptions.Controls.AddRange(new Control[] {
                chkPositionImages, lblRectX, numRectX, lblRectY, numRectY,
                lblRectWidth, numRectWidth, lblRectHeight, numRectHeight
            });
            panel.Controls.Add(grpPositionImageOptions);
        }

        /// <summary>
        /// 初始化表格删除标签页
        /// </summary>
        private void InitializeTableDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageTableDeletion.Controls.Add(panel);
            _tabPanels["TableDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用表格删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkTableDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 表格删除选项
            var grpTableOptions = CreateGroupBox("表格删除选项", 10, yPos, 720, 130);

            var chkAllTables = CreateCheckBox("删除所有表格", 15, 25);
            chkAllTables.Name = "chkDeleteAllTables";

            var chkLastSlideTables = CreateCheckBox("删除最后一页表格", 15, 50);
            chkLastSlideTables.Name = "chkDeleteLastSlideTables";

            var chkTableRange = CreateCheckBox("删除指定页范围的表格", 15, 75);
            chkTableRange.Name = "chkDeleteTableRange";
            var lblTableRangeFrom = CreateLabel("从第", 250, 75);
            var numTableRangeStart = CreateNumericUpDown(1, 9999, 290, 72);
            numTableRangeStart.Name = "numTableRangeStart";
            var lblTableRangeTo = CreateLabel("页到第", 370, 75);
            var numTableRangeEnd = CreateNumericUpDown(1, 9999, 420, 72);
            numTableRangeEnd.Name = "numTableRangeEnd";
            var lblTableRangeEndText = CreateLabel("页", 500, 75);

            grpTableOptions.Controls.AddRange(new Control[] {
                chkAllTables, chkLastSlideTables, chkTableRange,
                lblTableRangeFrom, numTableRangeStart, lblTableRangeTo,
                numTableRangeEnd, lblTableRangeEndText
            });
            panel.Controls.Add(grpTableOptions);
        }

        /// <summary>
        /// 初始化图表删除标签页
        /// </summary>
        private void InitializeChartDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageChartDeletion.Controls.Add(panel);
            _tabPanels["ChartDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用图表删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkChartDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 图表删除选项
            var grpChartOptions = CreateGroupBox("图表删除选项", 10, yPos, 720, 130);

            var chkAllCharts = CreateCheckBox("删除所有图表", 15, 25);
            chkAllCharts.Name = "chkDeleteAllCharts";

            var chkLastSlideCharts = CreateCheckBox("删除最后一页图表", 15, 50);
            chkLastSlideCharts.Name = "chkDeleteLastSlideCharts";

            var chkChartRange = CreateCheckBox("删除指定页范围的图表", 15, 75);
            chkChartRange.Name = "chkDeleteChartRange";
            var lblChartRangeFrom = CreateLabel("从第", 250, 75);
            var numChartRangeStart = CreateNumericUpDown(1, 9999, 290, 72);
            numChartRangeStart.Name = "numChartRangeStart";
            var lblChartRangeTo = CreateLabel("页到第", 370, 75);
            var numChartRangeEnd = CreateNumericUpDown(1, 9999, 420, 72);
            numChartRangeEnd.Name = "numChartRangeEnd";
            var lblChartRangeEndText = CreateLabel("页", 500, 75);

            grpChartOptions.Controls.AddRange(new Control[] {
                chkAllCharts, chkLastSlideCharts, chkChartRange,
                lblChartRangeFrom, numChartRangeStart, lblChartRangeTo,
                numChartRangeEnd, lblChartRangeEndText
            });
            panel.Controls.Add(grpChartOptions);
        }

        /// <summary>
        /// 初始化音频视频删除标签页
        /// </summary>
        private void InitializeMediaDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageMediaDeletion.Controls.Add(panel);
            _tabPanels["MediaDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用音频视频删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkMediaDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 音频删除选项
            var grpAudioOptions = CreateGroupBox("音频删除选项", 10, yPos, 720, 130);

            var chkAllAudio = CreateCheckBox("删除所有音频", 15, 25);
            chkAllAudio.Name = "chkDeleteAllAudio";

            var chkLastSlideAudio = CreateCheckBox("删除最后一页音频", 15, 50);
            chkLastSlideAudio.Name = "chkDeleteLastSlideAudio";

            var chkAudioRange = CreateCheckBox("删除指定页范围的音频", 15, 75);
            chkAudioRange.Name = "chkDeleteAudioRange";
            var lblAudioRangeFrom = CreateLabel("从第", 250, 75);
            var numAudioRangeStart = CreateNumericUpDown(1, 9999, 290, 72);
            numAudioRangeStart.Name = "numAudioRangeStart";
            var lblAudioRangeTo = CreateLabel("页到第", 370, 75);
            var numAudioRangeEnd = CreateNumericUpDown(1, 9999, 420, 72);
            numAudioRangeEnd.Name = "numAudioRangeEnd";
            var lblAudioRangeEndText = CreateLabel("页", 500, 75);

            grpAudioOptions.Controls.AddRange(new Control[] {
                chkAllAudio, chkLastSlideAudio, chkAudioRange,
                lblAudioRangeFrom, numAudioRangeStart, lblAudioRangeTo,
                numAudioRangeEnd, lblAudioRangeEndText
            });
            panel.Controls.Add(grpAudioOptions);
            yPos += 140;

            // 视频删除选项
            var grpVideoOptions = CreateGroupBox("视频删除选项", 10, yPos, 720, 130);

            var chkAllVideo = CreateCheckBox("删除所有视频", 15, 25);
            chkAllVideo.Name = "chkDeleteAllVideo";

            var chkLastSlideVideo = CreateCheckBox("删除最后一页视频", 15, 50);
            chkLastSlideVideo.Name = "chkDeleteLastSlideVideo";

            var chkVideoRange = CreateCheckBox("删除指定页范围的视频", 15, 75);
            chkVideoRange.Name = "chkDeleteVideoRange";
            var lblVideoRangeFrom = CreateLabel("从第", 250, 75);
            var numVideoRangeStart = CreateNumericUpDown(1, 9999, 290, 72);
            numVideoRangeStart.Name = "numVideoRangeStart";
            var lblVideoRangeTo = CreateLabel("页到第", 370, 75);
            var numVideoRangeEnd = CreateNumericUpDown(1, 9999, 420, 72);
            numVideoRangeEnd.Name = "numVideoRangeEnd";
            var lblVideoRangeEndText = CreateLabel("页", 500, 75);

            grpVideoOptions.Controls.AddRange(new Control[] {
                chkAllVideo, chkLastSlideVideo, chkVideoRange,
                lblVideoRangeFrom, numVideoRangeStart, lblVideoRangeTo,
                numVideoRangeEnd, lblVideoRangeEndText
            });
            panel.Controls.Add(grpVideoOptions);
        }

        /// <summary>
        /// 初始化联系方式删除标签页
        /// </summary>
        private void InitializeContactDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageContactDeletion.Controls.Add(panel);
            _tabPanels["ContactDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用联系方式删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkContactDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 联系方式删除选项
            var grpContactOptions = CreateGroupBox("联系方式删除选项", 10, yPos, 720, 180);

            var chkPhoneNumbers = CreateCheckBox("删除包含手机号码的文本", 15, 30);
            chkPhoneNumbers.Name = "chkDeletePhoneNumbers";

            var chkLandlineNumbers = CreateCheckBox("删除包含固定电话的文本", 15, 55);
            chkLandlineNumbers.Name = "chkDeleteLandlineNumbers";

            var chkEmailAddresses = CreateCheckBox("删除包含邮箱地址的文本", 15, 80);
            chkEmailAddresses.Name = "chkDeleteEmailAddresses";

            var chkWebsites = CreateCheckBox("删除包含网址的文本", 15, 105);
            chkWebsites.Name = "chkDeleteWebsites";

            var chkHyperlinks = CreateCheckBox("删除所有超链接", 15, 130);
            chkHyperlinks.Name = "chkDeleteHyperlinks";

            grpContactOptions.Controls.AddRange(new Control[] {
                chkPhoneNumbers, chkLandlineNumbers, chkEmailAddresses,
                chkWebsites, chkHyperlinks
            });
            panel.Controls.Add(grpContactOptions);
        }

        /// <summary>
        /// 初始化动画删除标签页
        /// </summary>
        private void InitializeAnimationDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageAnimationDeletion.Controls.Add(panel);
            _tabPanels["AnimationDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用动画删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkAnimationDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 动画效果删除选项
            var grpAnimationOptions = CreateGroupBox("动画效果删除选项", 10, yPos, 720, 130);

            var chkAllAnimations = CreateCheckBox("删除所有动画效果", 15, 25);
            chkAllAnimations.Name = "chkDeleteAllAnimations";

            var chkLastSlideAnimations = CreateCheckBox("删除最后一页动画效果", 15, 50);
            chkLastSlideAnimations.Name = "chkDeleteLastSlideAnimations";

            var chkAnimationRange = CreateCheckBox("删除指定页范围的动画效果", 15, 75);
            chkAnimationRange.Name = "chkDeleteAnimationRange";
            var lblAnimationRangeFrom = CreateLabel("从第", 280, 75);
            var numAnimationRangeStart = CreateNumericUpDown(1, 9999, 320, 72);
            numAnimationRangeStart.Name = "numAnimationRangeStart";
            var lblAnimationRangeTo = CreateLabel("页到第", 400, 75);
            var numAnimationRangeEnd = CreateNumericUpDown(1, 9999, 450, 72);
            numAnimationRangeEnd.Name = "numAnimationRangeEnd";
            var lblAnimationRangeEndText = CreateLabel("页", 530, 75);

            grpAnimationOptions.Controls.AddRange(new Control[] {
                chkAllAnimations, chkLastSlideAnimations, chkAnimationRange,
                lblAnimationRangeFrom, numAnimationRangeStart, lblAnimationRangeTo,
                numAnimationRangeEnd, lblAnimationRangeEndText
            });
            panel.Controls.Add(grpAnimationOptions);
            yPos += 140;

            // 切换效果删除选项
            var grpTransitionOptions = CreateGroupBox("切换效果删除选项", 10, yPos, 720, 130);

            var chkAllTransitions = CreateCheckBox("删除所有切换效果", 15, 25);
            chkAllTransitions.Name = "chkDeleteAllTransitions";

            var chkLastSlideTransitions = CreateCheckBox("删除最后一页切换效果", 15, 50);
            chkLastSlideTransitions.Name = "chkDeleteLastSlideTransitions";

            var chkTransitionRange = CreateCheckBox("删除指定页范围的切换效果", 15, 75);
            chkTransitionRange.Name = "chkDeleteTransitionRange";
            var lblTransitionRangeFrom = CreateLabel("从第", 280, 75);
            var numTransitionRangeStart = CreateNumericUpDown(1, 9999, 320, 72);
            numTransitionRangeStart.Name = "numTransitionRangeStart";
            var lblTransitionRangeTo = CreateLabel("页到第", 400, 75);
            var numTransitionRangeEnd = CreateNumericUpDown(1, 9999, 450, 72);
            numTransitionRangeEnd.Name = "numTransitionRangeEnd";
            var lblTransitionRangeEndText = CreateLabel("页", 530, 75);

            grpTransitionOptions.Controls.AddRange(new Control[] {
                chkAllTransitions, chkLastSlideTransitions, chkTransitionRange,
                lblTransitionRangeFrom, numTransitionRangeStart, lblTransitionRangeTo,
                numTransitionRangeEnd, lblTransitionRangeEndText
            });
            panel.Controls.Add(grpTransitionOptions);
        }

        /// <summary>
        /// 初始化备注删除标签页
        /// </summary>
        private void InitializeNotesDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageNotesDeletion.Controls.Add(panel);
            _tabPanels["NotesDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用备注删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkNotesDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 备注删除选项
            var grpNotesOptions = CreateGroupBox("备注删除选项", 10, yPos, 720, 100);

            var chkDeleteSlideNotes = CreateCheckBox("删除幻灯片备注", 15, 25);
            chkDeleteSlideNotes.Name = "chkDeleteSlideNotes";

            var chkClearNotesContent = CreateCheckBox("清空备注内容", 15, 50);
            chkClearNotesContent.Name = "chkClearNotesContent";

            grpNotesOptions.Controls.AddRange(new Control[] {
                chkDeleteSlideNotes, chkClearNotesContent
            });
            panel.Controls.Add(grpNotesOptions);
        }

        /// <summary>
        /// 初始化格式删除标签页
        /// </summary>
        private void InitializeFormatDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageFormatDeletion.Controls.Add(panel);
            _tabPanels["FormatDeletion"] = panel;

            int yPos = 10;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用格式删除功能", 10, yPos);
            chkMasterSwitch.Name = "chkFormatDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 35;

            // 格式删除选项
            var grpFormatOptions = CreateGroupBox("格式删除选项", 10, yPos, 720, 130);

            var chkDeleteFontFormatting = CreateCheckBox("删除字体格式", 15, 25);
            chkDeleteFontFormatting.Name = "chkDeleteFontFormatting";

            var chkDeleteParagraphFormatting = CreateCheckBox("删除段落格式", 15, 50);
            chkDeleteParagraphFormatting.Name = "chkDeleteParagraphFormatting";

            var chkDeleteTableFormatting = CreateCheckBox("删除表格格式", 15, 75);
            chkDeleteTableFormatting.Name = "chkDeleteTableFormatting";

            var chkDeleteListFormatting = CreateCheckBox("删除列表格式", 15, 100);
            chkDeleteListFormatting.Name = "chkDeleteListFormatting";

            grpFormatOptions.Controls.AddRange(new Control[] {
                chkDeleteFontFormatting, chkDeleteParagraphFormatting,
                chkDeleteTableFormatting, chkDeleteListFormatting
            });
            panel.Controls.Add(grpFormatOptions);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // 按钮事件
                btnOK.Click += BtnOK_Click;
                btnCancel.Click += BtnCancel_Click;
                btnApply.Click += BtnApply_Click;
                btnReset.Click += BtnReset_Click;

                // 窗体事件
                this.Load += ContentDeletionForm_Load;
                this.FormClosing += ContentDeletionForm_FormClosing;

                // 非法词按钮事件
                SetupIllegalWordsButtonEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置事件处理器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置非法词按钮事件
        /// </summary>
        private void SetupIllegalWordsButtonEvents()
        {
            try
            {
                // 文件名非法词按钮
                var btnFileNameIllegal = FindControlByName(_tabPanels["DocumentDeletion"], "btnEditFileNameIllegalWords") as Button;
                if (btnFileNameIllegal != null)
                {
                    btnFileNameIllegal.Click += BtnFileNameIllegalWords_Click;
                }

                // 内容非法词按钮
                var btnContentIllegal = FindControlByName(_tabPanels["DocumentDeletion"], "btnEditContentIllegalWords") as Button;
                if (btnContentIllegal != null)
                {
                    btnContentIllegal.Click += BtnContentIllegalWords_Click;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置非法词按钮事件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载设置到界面
        /// </summary>
        private void LoadSettingsToUI()
        {
            try
            {
                // 加载删除文档设置
                LoadDocumentDeletionSettings();

                // 加载内容删除设置
                LoadContentRemovalSettings();

                // 加载文本删除设置
                LoadTextDeletionSettings();

                // 加载图片删除设置
                LoadImageDeletionSettings();

                // 加载表格删除设置
                LoadTableDeletionSettings();

                // 加载图表删除设置
                LoadChartDeletionSettings();

                // 加载音频视频删除设置
                LoadMediaDeletionSettings();

                // 加载联系方式删除设置
                LoadContactDeletionSettings();

                // 加载动画删除设置
                LoadAnimationDeletionSettings();

                // 加载备注删除设置
                LoadNotesDeletionSettings();

                // 加载格式删除设置
                LoadFormatDeletionSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置到界面失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载删除文档设置
        /// </summary>
        private void LoadDocumentDeletionSettings()
        {
            var settings = _currentSettings.DocumentDeletion;
            var panel = _tabPanels["DocumentDeletion"];

            // 文件名长度检查
            SetCheckBoxValue(panel, "chkFileNameLengthCheck", settings.EnableFileNameLengthCheck);
            SetNumericUpDownValue(panel, "numFileNameMinLength", settings.FileNameMinLength);
            SetNumericUpDownValue(panel, "numFileNameMaxLength", settings.FileNameMaxLength);

            // 文件大小检查
            SetCheckBoxValue(panel, "chkFileSizeCheck", settings.EnableFileSizeCheck);
            SetNumericUpDownValue(panel, "numFileSizeMin", settings.FileMinSize);
            SetNumericUpDownValue(panel, "numFileSizeMax", settings.FileMaxSize);
            SetComboBoxValue(panel, "cmbFileSizeUnit", settings.FileSizeUnit);

            // 内容字符数检查
            SetCheckBoxValue(panel, "chkContentCharCountCheck", settings.EnableContentCharCountCheck);
            SetNumericUpDownValue(panel, "numContentCharMin", settings.ContentMinCharCount);
            SetNumericUpDownValue(panel, "numContentCharMax", settings.ContentMaxCharCount);

            // 页数检查
            SetCheckBoxValue(panel, "chkPageCountCheck", settings.EnablePageCountCheck);
            SetNumericUpDownValue(panel, "numPageMin", settings.MinPageCount);
            SetNumericUpDownValue(panel, "numPageMax", settings.MaxPageCount);

            // 非法词检查
            SetCheckBoxValue(panel, "chkFileNameIllegalCheck", settings.EnableFileNameIllegalWordsCheck);
            UpdateIllegalWordsCountLabel("lblFileNameIllegalCount", settings.FileNameIllegalWords.Count);
            SetCheckBoxValue(panel, "chkContentIllegalCheck", settings.EnableContentIllegalWordsCheck);
            UpdateIllegalWordsCountLabel("lblContentIllegalCount", settings.ContentIllegalWords.Count);
        }

        /// <summary>
        /// 加载内容删除设置
        /// </summary>
        private void LoadContentRemovalSettings()
        {
            var settings = _currentSettings.ContentRemoval;
            var panel = _tabPanels["ContentRemoval"];

            SetCheckBoxValue(panel, "chkDeleteBlankSlides", settings.DeleteBlankSlides);
            SetCheckBoxValue(panel, "chkDeleteFirstSlide", settings.DeleteFirstSlide);
            SetCheckBoxValue(panel, "chkDeleteLastSlide", settings.DeleteLastSlide);
            SetCheckBoxValue(panel, "chkDeleteSlideRange", settings.EnableSlideRangeDeletion);
            SetNumericUpDownValue(panel, "numSlideRangeStart", settings.SlideRangeStart);
            SetNumericUpDownValue(panel, "numSlideRangeEnd", settings.SlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeleteKeywordSlides", settings.EnableKeywordSlidesDeletion);
            SetTextBoxValue(panel, "txtSlideKeywords", string.Join(",", settings.SlideKeywords));
            SetCheckBoxValue(panel, "chkDeleteBlankParagraphs", settings.DeleteBlankParagraphs);
            SetCheckBoxValue(panel, "chkDeleteBlankLines", settings.DeleteBlankLines);
        }

        /// <summary>
        /// 加载文本删除设置
        /// </summary>
        private void LoadTextDeletionSettings()
        {
            var settings = _currentSettings.TextDeletion;
            var panel = _tabPanels["TextDeletion"];

            SetCheckBoxValue(panel, "chkDeleteParagraphsWithText", settings.DeleteParagraphsWithText);
            SetTextBoxValue(panel, "txtParagraphKeywords", string.Join("\r\n", settings.ParagraphKeywords));
            SetCheckBoxValue(panel, "chkDeleteTextBoxesWithText", settings.DeleteTextBoxesWithText);
            SetTextBoxValue(panel, "txtTextBoxKeywords", string.Join("\r\n", settings.TextBoxKeywords));
            SetCheckBoxValue(panel, "chkDeleteTablesWithText", settings.DeleteTablesWithText);
            SetTextBoxValue(panel, "txtTableKeywords", string.Join("\r\n", settings.TableKeywords));
        }

        /// <summary>
        /// 加载图片删除设置
        /// </summary>
        private void LoadImageDeletionSettings()
        {
            var settings = _currentSettings.ImageDeletion;
            var panel = _tabPanels["ImageDeletion"];

            SetCheckBoxValue(panel, "chkDeleteAllImages", settings.DeleteAllImages);
            SetCheckBoxValue(panel, "chkDeleteSpecificImages", settings.EnableSpecificImageDeletion);
            SetTextBoxValue(panel, "txtSpecificImageNames", string.Join(",", settings.SpecificImageNames));
            SetCheckBoxValue(panel, "chkDeleteLastSlideImages", settings.DeleteLastSlideImages);
            SetCheckBoxValue(panel, "chkDeleteBackgroundImages", settings.DeleteBackgroundImages);
            SetCheckBoxValue(panel, "chkDeleteImageRange", settings.EnableSlideRangeImageDeletion);
            SetNumericUpDownValue(panel, "numImageRangeStart", settings.ImageSlideRangeStart);
            SetNumericUpDownValue(panel, "numImageRangeEnd", settings.ImageSlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeletePositionImages", settings.EnablePositionImageDeletion);
            SetNumericUpDownValue(panel, "numPositionX", (decimal)settings.PositionX);
            SetNumericUpDownValue(panel, "numPositionY", (decimal)settings.PositionY);
            SetNumericUpDownValue(panel, "numPositionWidth", (decimal)settings.PositionWidth);
            SetNumericUpDownValue(panel, "numPositionHeight", (decimal)settings.PositionHeight);
        }

        /// <summary>
        /// 加载表格删除设置
        /// </summary>
        private void LoadTableDeletionSettings()
        {
            var settings = _currentSettings.TableDeletion;
            var panel = _tabPanels["TableDeletion"];

            SetCheckBoxValue(panel, "chkDeleteAllTables", settings.DeleteAllTables);
            SetCheckBoxValue(panel, "chkDeleteLastSlideTables", settings.DeleteLastSlideTables);
            SetCheckBoxValue(panel, "chkDeleteTableRange", settings.EnableSlideRangeTableDeletion);
            SetNumericUpDownValue(panel, "numTableRangeStart", settings.TableSlideRangeStart);
            SetNumericUpDownValue(panel, "numTableRangeEnd", settings.TableSlideRangeEnd);
        }

        /// <summary>
        /// 加载图表删除设置
        /// </summary>
        private void LoadChartDeletionSettings()
        {
            var settings = _currentSettings.ChartDeletion;
            var panel = _tabPanels["ChartDeletion"];

            SetCheckBoxValue(panel, "chkDeleteAllCharts", settings.DeleteAllCharts);
            SetCheckBoxValue(panel, "chkDeleteLastSlideCharts", settings.DeleteLastSlideCharts);
            SetCheckBoxValue(panel, "chkDeleteChartRange", settings.EnableSlideRangeChartDeletion);
            SetNumericUpDownValue(panel, "numChartRangeStart", settings.ChartSlideRangeStart);
            SetNumericUpDownValue(panel, "numChartRangeEnd", settings.ChartSlideRangeEnd);
        }

        /// <summary>
        /// 加载音频视频删除设置
        /// </summary>
        private void LoadMediaDeletionSettings()
        {
            var settings = _currentSettings.MediaDeletion;
            var panel = _tabPanels["MediaDeletion"];

            SetCheckBoxValue(panel, "chkDeleteAllAudio", settings.DeleteAllAudio);
            SetCheckBoxValue(panel, "chkDeleteLastSlideAudio", settings.DeleteLastSlideAudio);
            SetCheckBoxValue(panel, "chkDeleteAudioRange", settings.EnableSlideRangeAudioDeletion);
            SetNumericUpDownValue(panel, "numAudioRangeStart", settings.AudioSlideRangeStart);
            SetNumericUpDownValue(panel, "numAudioRangeEnd", settings.AudioSlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeleteAllVideo", settings.DeleteAllVideo);
            SetCheckBoxValue(panel, "chkDeleteLastSlideVideo", settings.DeleteLastSlideVideo);
            SetCheckBoxValue(panel, "chkDeleteVideoRange", settings.EnableSlideRangeVideoDeletion);
            SetNumericUpDownValue(panel, "numVideoRangeStart", settings.VideoSlideRangeStart);
            SetNumericUpDownValue(panel, "numVideoRangeEnd", settings.VideoSlideRangeEnd);
        }

        /// <summary>
        /// 加载联系方式删除设置
        /// </summary>
        private void LoadContactDeletionSettings()
        {
            var settings = _currentSettings.ContactDeletion;
            var panel = _tabPanels["ContactDeletion"];

            SetCheckBoxValue(panel, "chkDeletePhoneNumbers", settings.DeletePhoneNumbers);
            SetCheckBoxValue(panel, "chkDeleteLandlineNumbers", settings.DeleteLandlineNumbers);
            SetCheckBoxValue(panel, "chkDeleteEmailAddresses", settings.DeleteEmailAddresses);
            SetCheckBoxValue(panel, "chkDeleteWebsites", settings.DeleteWebsites);
            SetCheckBoxValue(panel, "chkDeleteHyperlinks", settings.DeleteHyperlinks);
        }

        /// <summary>
        /// 加载动画删除设置
        /// </summary>
        private void LoadAnimationDeletionSettings()
        {
            var settings = _currentSettings.AnimationDeletion;
            var panel = _tabPanels["AnimationDeletion"];

            SetCheckBoxValue(panel, "chkDeleteAllAnimations", settings.DeleteAllAnimations);
            SetCheckBoxValue(panel, "chkDeleteLastSlideAnimations", settings.DeleteLastSlideAnimations);
            SetCheckBoxValue(panel, "chkDeleteAnimationRange", settings.EnableSlideRangeAnimationDeletion);
            SetNumericUpDownValue(panel, "numAnimationRangeStart", settings.AnimationSlideRangeStart);
            SetNumericUpDownValue(panel, "numAnimationRangeEnd", settings.AnimationSlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeleteAllTransitions", settings.DeleteAllTransitions);
            SetCheckBoxValue(panel, "chkDeleteLastSlideTransitions", settings.DeleteLastSlideTransitions);
            SetCheckBoxValue(panel, "chkDeleteTransitionRange", settings.EnableSlideRangeTransitionDeletion);
            SetNumericUpDownValue(panel, "numTransitionRangeStart", settings.TransitionSlideRangeStart);
            SetNumericUpDownValue(panel, "numTransitionRangeEnd", settings.TransitionSlideRangeEnd);
        }

        /// <summary>
        /// 加载备注删除设置
        /// </summary>
        private void LoadNotesDeletionSettings()
        {
            var settings = _currentSettings.NotesDeletion;
            var panel = _tabPanels["NotesDeletion"];

            SetCheckBoxValue(panel, "chkDeleteSlideNotes", settings.DeleteSlideNotes);
            SetCheckBoxValue(panel, "chkClearNotesContent", settings.ClearNotesContent);
        }

        /// <summary>
        /// 加载格式删除设置
        /// </summary>
        private void LoadFormatDeletionSettings()
        {
            var settings = _currentSettings.FormatDeletion;
            var panel = _tabPanels["FormatDeletion"];

            SetCheckBoxValue(panel, "chkDeleteFontFormatting", settings.DeleteFontFormatting);
            SetCheckBoxValue(panel, "chkDeleteParagraphFormatting", settings.DeleteParagraphFormatting);
            SetCheckBoxValue(panel, "chkDeleteTableFormatting", settings.DeleteTableFormatting);
            SetCheckBoxValue(panel, "chkDeleteListFormatting", settings.DeleteListFormatting);
        }

        #region 事件处理方法

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void ContentDeletionForm_Load(object? sender, EventArgs e)
        {
            try
            {
                // 窗体加载完成后的初始化工作
                this.Text = "内容删除设置";

                // 调整标签页宽度以充分利用空间
                AdjustTabWidth();

                // 监听窗体大小变化事件
                this.Resize += ContentDeletionForm_Resize;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"窗体加载失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体大小变化事件
        /// </summary>
        private void ContentDeletionForm_Resize(object? sender, EventArgs e)
        {
            try
            {
                // 窗体大小变化时重新调整标签页宽度
                AdjustTabWidth();
            }
            catch (Exception ex)
            {
                // 忽略调整过程中的错误，避免影响用户体验
            }
        }

        /// <summary>
        /// 调整标签页宽度以充分利用空间
        /// </summary>
        private void AdjustTabWidth()
        {
            try
            {
                if (tabControlMain == null) return;

                // 获取TabControl的可用宽度
                var availableWidth = tabControlMain.ClientSize.Width;

                // 考虑TabControl的内部边距（通常左右各有2-3px的边距）
                var usableWidth = availableWidth - 6;

                // 总共11个标签页，分为两行：第一行6个，第二行5个
                // 为了保持一致性，使用第一行的宽度（6个标签）作为标准
                var tabWidth = usableWidth / 6;

                // 确保宽度不小于最小值（避免标签太窄）
                if (tabWidth < 80)
                    tabWidth = 80;

                // 确保宽度不大于最大值（避免标签太宽）
                if (tabWidth > 150)
                    tabWidth = 150;

                // 设置新的标签大小
                tabControlMain.ItemSize = new Size(tabWidth, 25);
            }
            catch (Exception ex)
            {
                // 忽略调整过程中的错误
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ContentDeletionForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            try
            {
                // 如果是通过确定或应用按钮关闭，保存设置
                if (this.DialogResult == DialogResult.OK)
                {
                    SaveUIToSettings();
                    SaveSettingsToConfig();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToSettings();
                SaveSettingsToConfig();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToSettings();
                SaveSettingsToConfig();
                MessageBox.Show("设置已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要重置所有设置为默认值吗？", "确认重置",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _currentSettings = new Models.ContentDeletionSettings();
                    LoadSettingsToUI();
                    MessageBox.Show("设置已重置为默认值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 文件名非法词按钮点击事件
        /// </summary>
        private void BtnFileNameIllegalWords_Click(object? sender, EventArgs e)
        {
            try
            {
                using var form = new IllegalWordsForm(
                    "文件名非法词设置",
                    "请输入文件名非法词，每行一个。如果文件名包含任何一个非法词，该文件将被删除。",
                    _currentSettings.DocumentDeletion.FileNameIllegalWords);

                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.DocumentDeletion.FileNameIllegalWords = form.GetIllegalWords();
                    UpdateIllegalWordsCountLabel("lblFileNameIllegalCount", _currentSettings.DocumentDeletion.FileNameIllegalWords.Count);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置文件名非法词失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 内容非法词按钮点击事件
        /// </summary>
        private void BtnContentIllegalWords_Click(object? sender, EventArgs e)
        {
            try
            {
                using var form = new IllegalWordsForm(
                    "内容非法词设置",
                    "请输入内容非法词，每行一个。如果文档内容包含任何一个非法词，该文件将被删除。",
                    _currentSettings.DocumentDeletion.ContentIllegalWords);

                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.DocumentDeletion.ContentIllegalWords = form.GetIllegalWords();
                    UpdateIllegalWordsCountLabel("lblContentIllegalCount", _currentSettings.DocumentDeletion.ContentIllegalWords.Count);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置内容非法词失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新非法词数量标签
        /// </summary>
        private void UpdateIllegalWordsCountLabel(string labelName, int count)
        {
            try
            {
                var label = FindControlByName(_tabPanels["DocumentDeletion"], labelName) as Label;
                if (label != null)
                {
                    label.Text = $"{count}个";
                    label.ForeColor = count > 0 ? Color.Blue : Color.Gray;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新非法词数量标签失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        /// <summary>
        /// 保存设置到配置文件
        /// </summary>
        private void SaveSettingsToConfig()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                config.ContentDeletionSettings = _currentSettings;
                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
