<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>

    <AssemblyTitle>PPT批量处理工具</AssemblyTitle>
    <AssemblyDescription>基于Aspose.Slides的PPT批量处理软件</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="Aspose.Slides">
      <HintPath>Aspose.Slides.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Text.Json" Version="8.0.4" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Aspose.Total.NET.lic">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Aspose.Slides.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Config\" />
    <Folder Include="Log\" />
    <Folder Include="Forms\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
  </ItemGroup>

</Project>
