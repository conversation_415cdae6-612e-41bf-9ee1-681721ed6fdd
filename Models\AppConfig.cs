using System;
using System.Collections.Generic;

namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// 应用程序主配置类
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 路径设置
        /// </summary>
        public PathSettings PathSettings { get; set; } = new PathSettings();

        /// <summary>
        /// 处理设置
        /// </summary>
        public ProcessSettings ProcessSettings { get; set; } = new ProcessSettings();

        /// <summary>
        /// 功能启用状态
        /// </summary>
        public Dictionary<string, bool> FunctionEnabled { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// 日志设置
        /// </summary>
        public LogSettings LogSettings { get; set; } = new LogSettings();

        /// <summary>
        /// 窗体设置
        /// </summary>
        public FormSettings FormSettings { get; set; } = new FormSettings();

        /// <summary>
        /// 页面设置
        /// </summary>
        public PageSetupSettings PageSetupSettings { get; set; } = new PageSetupSettings();

        /// <summary>
        /// 背景设置
        /// </summary>
        public BackgroundSettings BackgroundSettings { get; set; } = new BackgroundSettings();

        /// <summary>
        /// 内容删除设置
        /// </summary>
        public ContentDeletionSettings ContentDeletionSettings { get; set; } = new ContentDeletionSettings();
    }

    /// <summary>
    /// 路径设置
    /// </summary>
    public class PathSettings
    {
        /// <summary>
        /// 源目录路径
        /// </summary>
        public string SourcePath { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录路径
        /// </summary>
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否包含子目录
        /// </summary>
        public bool IncludeSubfolders { get; set; } = true;

        /// <summary>
        /// 是否保持目录结构
        /// </summary>
        public bool KeepDirectoryStructure { get; set; } = true;
    }

    /// <summary>
    /// 处理设置
    /// </summary>
    public class ProcessSettings
    {
        /// <summary>
        /// 是否复制文件（true=复制，false=移动）
        /// </summary>
        public bool CopyFiles { get; set; } = true;

        /// <summary>
        /// 是否直接处理源文件
        /// </summary>
        public bool ProcessSourceDirectly { get; set; } = false;

        /// <summary>
        /// 线程数
        /// </summary>
        public int ThreadCount { get; set; } = 1;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 50;

        /// <summary>
        /// 支持的文件格式
        /// </summary>
        public List<string> SupportedFormats { get; set; } = new List<string>
        {
            ".ppt", ".pptx", ".pptm", ".ppsx", ".ppsm", ".potx", ".potm"
        };
    }

    /// <summary>
    /// 日志设置
    /// </summary>
    public class LogSettings
    {
        /// <summary>
        /// 是否启用日志
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel LogLevel { get; set; } = LogLevel.Info;

        /// <summary>
        /// 启用的日志类型
        /// </summary>
        public Dictionary<string, bool> EnabledLogTypes { get; set; } = new Dictionary<string, bool>
        {
            { "处理开始", true },
            { "处理完成", true },
            { "处理错误", true },
            { "文件操作", true },
            { "配置变更", false },
            { "调试信息", false }
        };

        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 10;

        /// <summary>
        /// 保留日志文件天数
        /// </summary>
        public int LogRetentionDays { get; set; } = 30;
    }

    /// <summary>
    /// 窗体设置
    /// </summary>
    public class FormSettings
    {
        /// <summary>
        /// 窗体位置X
        /// </summary>
        public int LocationX { get; set; } = -1;

        /// <summary>
        /// 窗体位置Y
        /// </summary>
        public int LocationY { get; set; } = -1;

        /// <summary>
        /// 窗体宽度
        /// </summary>
        public int Width { get; set; } = 1024;

        /// <summary>
        /// 窗体高度
        /// </summary>
        public int Height { get; set; } = 768;

        /// <summary>
        /// 窗体状态
        /// </summary>
        public int WindowState { get; set; } = 0; // Normal
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3
    }

    /// <summary>
    /// 处理选项类
    /// </summary>
    public class ProcessingOptions
    {
        /// <summary>
        /// 源目录路径
        /// </summary>
        public string SourcePath { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录路径
        /// </summary>
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否包含子目录
        /// </summary>
        public bool IncludeSubfolders { get; set; } = true;

        /// <summary>
        /// 是否保持目录结构
        /// </summary>
        public bool KeepDirectoryStructure { get; set; } = true;

        /// <summary>
        /// 是否复制文件（true=复制，false=移动）
        /// </summary>
        public bool CopyFiles { get; set; } = true;

        /// <summary>
        /// 是否直接处理源文件
        /// </summary>
        public bool ProcessSourceDirectly { get; set; } = false;

        /// <summary>
        /// 线程数
        /// </summary>
        public int ThreadCount { get; set; } = 1;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 50;

        /// <summary>
        /// 支持的文件格式
        /// </summary>
        public List<string> SupportedFormats { get; set; } = new List<string>();
    }

    /// <summary>
    /// 处理统计信息类
    /// </summary>
    public class ProcessingStats
    {
        /// <summary>
        /// 总文件数
        /// </summary>
        public int TotalFiles { get; set; } = 0;

        /// <summary>
        /// 成功处理数
        /// </summary>
        public int SuccessCount { get; set; } = 0;

        /// <summary>
        /// 失败处理数
        /// </summary>
        public int FailureCount { get; set; } = 0;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; } = null;

        /// <summary>
        /// 处理进度百分比
        /// </summary>
        public double ProgressPercentage => TotalFiles > 0 ? (double)(SuccessCount + FailureCount) / TotalFiles * 100 : 0;

        /// <summary>
        /// 成功率百分比
        /// </summary>
        public double SuccessRate => (SuccessCount + FailureCount) > 0 ? (double)SuccessCount / (SuccessCount + FailureCount) * 100 : 0;

        /// <summary>
        /// 处理耗时
        /// </summary>
        public TimeSpan ElapsedTime => (EndTime ?? DateTime.Now) - StartTime;

        /// <summary>
        /// 处理速度（文件/分钟）
        /// </summary>
        public double ProcessingSpeed
        {
            get
            {
                var elapsed = ElapsedTime.TotalMinutes;
                return elapsed > 0 ? (SuccessCount + FailureCount) / elapsed : 0;
            }
        }
    }

    /// <summary>
    /// 处理进度事件参数
    /// </summary>
    public class ProcessProgressEventArgs : EventArgs
    {
        /// <summary>
        /// 进度百分比
        /// </summary>
        public int ProgressPercentage { get; }

        /// <summary>
        /// 处理统计信息
        /// </summary>
        public ProcessingStats Stats { get; }

        public ProcessProgressEventArgs(int progressPercentage, ProcessingStats stats)
        {
            ProgressPercentage = progressPercentage;
            Stats = stats;
        }
    }

    /// <summary>
    /// 处理完成事件参数
    /// </summary>
    public class ProcessCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 处理统计信息
        /// </summary>
        public ProcessingStats Stats { get; }

        /// <summary>
        /// 完成消息
        /// </summary>
        public string Message { get; }

        public ProcessCompletedEventArgs(ProcessingStats stats, string message)
        {
            Stats = stats;
            Message = message;
        }
    }

    /// <summary>
    /// 文件处理事件参数
    /// </summary>
    public class FileProcessedEventArgs : EventArgs
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; }

        public FileProcessedEventArgs(string fileName, bool isSuccess, string? errorMessage)
        {
            FileName = fileName;
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
        }
    }

    /// <summary>
    /// 页面设置配置类
    /// </summary>
    public class PageSetupSettings
    {
        /// <summary>
        /// 幻灯片宽度
        /// </summary>
        public float Width { get; set; } = 25.4f; // 默认25.4厘米

        /// <summary>
        /// 幻灯片高度
        /// </summary>
        public float Height { get; set; } = 19.05f; // 默认19.05厘米

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = "厘米";

        /// <summary>
        /// 是否横向（true=横向，false=纵向）
        /// </summary>
        public bool IsLandscape { get; set; } = true;

        /// <summary>
        /// 尺寸类型（Standard, Widescreen, Custom）
        /// </summary>
        public string SizeType { get; set; } = "Standard";

        /// <summary>
        /// 宽高比例
        /// </summary>
        public float AspectRatio { get; set; } = 4f / 3f;

        /// <summary>
        /// 是否锁定宽高比例
        /// </summary>
        public bool MaintainAspectRatio { get; set; } = true;

        /// <summary>
        /// 预设尺寸名称
        /// </summary>
        public string PresetName { get; set; } = "标准 4:3 (25.4 x 19.05 cm)";

        /// <summary>
        /// 获取以毫米为单位的宽度
        /// </summary>
        public float GetWidthInMM()
        {
            return ConvertToMM(Width, Unit);
        }

        /// <summary>
        /// 获取以毫米为单位的高度
        /// </summary>
        public float GetHeightInMM()
        {
            return ConvertToMM(Height, Unit);
        }

        /// <summary>
        /// 转换到毫米单位
        /// </summary>
        private float ConvertToMM(float value, string unit)
        {
            return unit switch
            {
                "毫米" => value,
                "厘米" => value * 10f,
                "英寸" => value * 25.4f,
                "磅" => value * 0.352778f,
                "像素" => value * 0.264583f, // 96 DPI
                _ => value * 10f // 默认厘米
            };
        }

        /// <summary>
        /// 获取友好的尺寸描述
        /// </summary>
        public string GetSizeDescription()
        {
            var ratioText = AspectRatio switch
            {
                var r when Math.Abs(r - 4f / 3f) < 0.01f => "4:3",
                var r when Math.Abs(r - 16f / 9f) < 0.01f => "16:9",
                var r when Math.Abs(r - 16f / 10f) < 0.01f => "16:10",
                var r when Math.Abs(r - 1f) < 0.01f => "1:1",
                _ => $"{AspectRatio:F2}:1"
            };

            var orientationText = IsLandscape ? "横向" : "纵向";
            return $"{Width:F1} x {Height:F1} {Unit} ({ratioText}, {orientationText})";
        }

        /// <summary>
        /// 克隆设置
        /// </summary>
        public PageSetupSettings Clone()
        {
            return new PageSetupSettings
            {
                Width = this.Width,
                Height = this.Height,
                Unit = this.Unit,
                IsLandscape = this.IsLandscape,
                SizeType = this.SizeType,
                AspectRatio = this.AspectRatio,
                MaintainAspectRatio = this.MaintainAspectRatio,
                PresetName = this.PresetName
            };
        }
    }

    /// <summary>
    /// 背景设置配置类
    /// </summary>
    public class BackgroundSettings
    {
        /// <summary>
        /// 背景类型（None, SolidColor, Gradient, Image）
        /// </summary>
        public string BackgroundType { get; set; } = "None";

        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 渐变起始颜色
        /// </summary>
        public string GradientStartColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 渐变结束颜色
        /// </summary>
        public string GradientEndColor { get; set; } = "#000000";

        /// <summary>
        /// 渐变方向（Horizontal, Vertical, Diagonal）
        /// </summary>
        public string GradientDirection { get; set; } = "Horizontal";

        /// <summary>
        /// 背景图片路径
        /// </summary>
        public string ImagePath { get; set; } = string.Empty;

        /// <summary>
        /// 图片填充模式（Stretch, Tile, Center, Fit, Fill）
        /// </summary>
        public string ImageFillMode { get; set; } = "Stretch";

        /// <summary>
        /// 图片透明度（0-100）
        /// </summary>
        public int ImageTransparency { get; set; } = 100;

        /// <summary>
        /// 克隆设置
        /// </summary>
        public BackgroundSettings Clone()
        {
            return new BackgroundSettings
            {
                BackgroundType = this.BackgroundType,
                BackgroundColor = this.BackgroundColor,
                GradientStartColor = this.GradientStartColor,
                GradientEndColor = this.GradientEndColor,
                GradientDirection = this.GradientDirection,
                ImagePath = this.ImagePath,
                ImageFillMode = this.ImageFillMode,
                ImageTransparency = this.ImageTransparency
            };
        }
    }

    /// <summary>
    /// 内容删除设置配置类
    /// </summary>
    public class ContentDeletionSettings
    {
        /// <summary>
        /// 删除文档设置
        /// </summary>
        public DocumentDeletionSettings DocumentDeletion { get; set; } = new DocumentDeletionSettings();

        /// <summary>
        /// 内容删除设置
        /// </summary>
        public ContentRemovalSettings ContentRemoval { get; set; } = new ContentRemovalSettings();

        /// <summary>
        /// 文本删除设置
        /// </summary>
        public TextDeletionSettings TextDeletion { get; set; } = new TextDeletionSettings();

        /// <summary>
        /// 图片删除设置
        /// </summary>
        public ImageDeletionSettings ImageDeletion { get; set; } = new ImageDeletionSettings();

        /// <summary>
        /// 表格删除设置
        /// </summary>
        public TableDeletionSettings TableDeletion { get; set; } = new TableDeletionSettings();

        /// <summary>
        /// 图表删除设置
        /// </summary>
        public ChartDeletionSettings ChartDeletion { get; set; } = new ChartDeletionSettings();

        /// <summary>
        /// 音频视频删除设置
        /// </summary>
        public MediaDeletionSettings MediaDeletion { get; set; } = new MediaDeletionSettings();

        /// <summary>
        /// 联系方式删除设置
        /// </summary>
        public ContactDeletionSettings ContactDeletion { get; set; } = new ContactDeletionSettings();

        /// <summary>
        /// 动画删除设置
        /// </summary>
        public AnimationDeletionSettings AnimationDeletion { get; set; } = new AnimationDeletionSettings();

        /// <summary>
        /// 备注删除设置
        /// </summary>
        public NotesDeletionSettings NotesDeletion { get; set; } = new NotesDeletionSettings();

        /// <summary>
        /// 格式删除设置
        /// </summary>
        public FormatDeletionSettings FormatDeletion { get; set; } = new FormatDeletionSettings();
    }

    /// <summary>
    /// 删除文档设置
    /// </summary>
    public class DocumentDeletionSettings
    {
        /// <summary>
        /// 是否启用文件名长度检查
        /// </summary>
        public bool EnableFileNameLengthCheck { get; set; } = false;

        /// <summary>
        /// 文件名最小长度
        /// </summary>
        public int FileNameMinLength { get; set; } = 1;

        /// <summary>
        /// 文件名最大长度
        /// </summary>
        public int FileNameMaxLength { get; set; } = 100;

        /// <summary>
        /// 是否启用文件大小检查
        /// </summary>
        public bool EnableFileSizeCheck { get; set; } = false;

        /// <summary>
        /// 文件最小大小
        /// </summary>
        public long FileMinSize { get; set; } = 1;

        /// <summary>
        /// 文件最大大小
        /// </summary>
        public long FileMaxSize { get; set; } = 10240; // 10MB

        /// <summary>
        /// 文件大小单位（B, KB, MB）
        /// </summary>
        public string FileSizeUnit { get; set; } = "KB";

        /// <summary>
        /// 是否启用内容字符数检查
        /// </summary>
        public bool EnableContentCharCountCheck { get; set; } = false;

        /// <summary>
        /// 内容最小字符数
        /// </summary>
        public int ContentMinCharCount { get; set; } = 1;

        /// <summary>
        /// 内容最大字符数
        /// </summary>
        public int ContentMaxCharCount { get; set; } = 100000;

        /// <summary>
        /// 是否启用页数检查
        /// </summary>
        public bool EnablePageCountCheck { get; set; } = false;

        /// <summary>
        /// 最小页数
        /// </summary>
        public int MinPageCount { get; set; } = 2;

        /// <summary>
        /// 最大页数
        /// </summary>
        public int MaxPageCount { get; set; } = 200;

        /// <summary>
        /// 是否启用文件名非法词检查
        /// </summary>
        public bool EnableFileNameIllegalWordsCheck { get; set; } = false;

        /// <summary>
        /// 文件名非法词列表
        /// </summary>
        public List<string> FileNameIllegalWords { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用内容非法词检查
        /// </summary>
        public bool EnableContentIllegalWordsCheck { get; set; } = false;

        /// <summary>
        /// 内容非法词列表
        /// </summary>
        public List<string> ContentIllegalWords { get; set; } = new List<string>();
    }

    /// <summary>
    /// 内容删除设置
    /// </summary>
    public class ContentRemovalSettings
    {
        /// <summary>
        /// 是否删除空白幻灯片
        /// </summary>
        public bool DeleteBlankSlides { get; set; } = false;

        /// <summary>
        /// 是否删除第一页
        /// </summary>
        public bool DeleteFirstSlide { get; set; } = false;

        /// <summary>
        /// 是否删除最后一页
        /// </summary>
        public bool DeleteLastSlide { get; set; } = false;

        /// <summary>
        /// 是否启用指定页范围删除
        /// </summary>
        public bool EnableSlideRangeDeletion { get; set; } = false;

        /// <summary>
        /// 删除页范围开始页
        /// </summary>
        public int SlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 删除页范围结束页
        /// </summary>
        public int SlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否启用包含关键词的页删除
        /// </summary>
        public bool EnableKeywordSlidesDeletion { get; set; } = false;

        /// <summary>
        /// 页删除关键词列表
        /// </summary>
        public List<string> SlideKeywords { get; set; } = new List<string>();

        /// <summary>
        /// 是否删除空白段落
        /// </summary>
        public bool DeleteBlankParagraphs { get; set; } = false;

        /// <summary>
        /// 是否删除空白行
        /// </summary>
        public bool DeleteBlankLines { get; set; } = false;
    }

    /// <summary>
    /// 文本删除设置
    /// </summary>
    public class TextDeletionSettings
    {
        /// <summary>
        /// 是否删除包含指定文本的段落
        /// </summary>
        public bool DeleteParagraphsWithText { get; set; } = false;

        /// <summary>
        /// 段落删除关键词列表
        /// </summary>
        public List<string> ParagraphKeywords { get; set; } = new List<string>();

        /// <summary>
        /// 是否删除包含指定文本的文本框
        /// </summary>
        public bool DeleteTextBoxesWithText { get; set; } = false;

        /// <summary>
        /// 文本框删除关键词列表
        /// </summary>
        public List<string> TextBoxKeywords { get; set; } = new List<string>();

        /// <summary>
        /// 是否删除包含指定文本的表格
        /// </summary>
        public bool DeleteTablesWithText { get; set; } = false;

        /// <summary>
        /// 表格删除关键词列表
        /// </summary>
        public List<string> TableKeywords { get; set; } = new List<string>();
    }

    /// <summary>
    /// 图片删除设置
    /// </summary>
    public class ImageDeletionSettings
    {
        /// <summary>
        /// 是否删除所有图片
        /// </summary>
        public bool DeleteAllImages { get; set; } = false;

        /// <summary>
        /// 是否启用指定图片删除
        /// </summary>
        public bool EnableSpecificImageDeletion { get; set; } = false;

        /// <summary>
        /// 指定删除的图片名称列表
        /// </summary>
        public List<string> SpecificImageNames { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用页范围图片删除
        /// </summary>
        public bool EnableSlideRangeImageDeletion { get; set; } = false;

        /// <summary>
        /// 图片删除页范围开始页
        /// </summary>
        public int ImageSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 图片删除页范围结束页
        /// </summary>
        public int ImageSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页图片
        /// </summary>
        public bool DeleteLastSlideImages { get; set; } = false;

        /// <summary>
        /// 是否启用位置条件图片删除
        /// </summary>
        public bool EnablePositionImageDeletion { get; set; } = false;

        /// <summary>
        /// 位置区域左边距
        /// </summary>
        public float PositionX { get; set; } = 0;

        /// <summary>
        /// 位置区域上边距
        /// </summary>
        public float PositionY { get; set; } = 0;

        /// <summary>
        /// 位置区域宽度
        /// </summary>
        public float PositionWidth { get; set; } = 100;

        /// <summary>
        /// 位置区域高度
        /// </summary>
        public float PositionHeight { get; set; } = 100;

        /// <summary>
        /// 是否删除背景图片
        /// </summary>
        public bool DeleteBackgroundImages { get; set; } = false;
    }

    /// <summary>
    /// 表格删除设置
    /// </summary>
    public class TableDeletionSettings
    {
        /// <summary>
        /// 是否删除所有表格
        /// </summary>
        public bool DeleteAllTables { get; set; } = false;

        /// <summary>
        /// 是否启用页范围表格删除
        /// </summary>
        public bool EnableSlideRangeTableDeletion { get; set; } = false;

        /// <summary>
        /// 表格删除页范围开始页
        /// </summary>
        public int TableSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 表格删除页范围结束页
        /// </summary>
        public int TableSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页表格
        /// </summary>
        public bool DeleteLastSlideTables { get; set; } = false;
    }

    /// <summary>
    /// 图表删除设置
    /// </summary>
    public class ChartDeletionSettings
    {
        /// <summary>
        /// 是否删除所有图表
        /// </summary>
        public bool DeleteAllCharts { get; set; } = false;

        /// <summary>
        /// 是否启用页范围图表删除
        /// </summary>
        public bool EnableSlideRangeChartDeletion { get; set; } = false;

        /// <summary>
        /// 图表删除页范围开始页
        /// </summary>
        public int ChartSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 图表删除页范围结束页
        /// </summary>
        public int ChartSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页图表
        /// </summary>
        public bool DeleteLastSlideCharts { get; set; } = false;
    }

    /// <summary>
    /// 音频视频删除设置
    /// </summary>
    public class MediaDeletionSettings
    {
        /// <summary>
        /// 是否删除所有音频
        /// </summary>
        public bool DeleteAllAudio { get; set; } = false;

        /// <summary>
        /// 是否启用页范围音频删除
        /// </summary>
        public bool EnableSlideRangeAudioDeletion { get; set; } = false;

        /// <summary>
        /// 音频删除页范围开始页
        /// </summary>
        public int AudioSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 音频删除页范围结束页
        /// </summary>
        public int AudioSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页音频
        /// </summary>
        public bool DeleteLastSlideAudio { get; set; } = false;

        /// <summary>
        /// 是否删除所有视频
        /// </summary>
        public bool DeleteAllVideo { get; set; } = false;

        /// <summary>
        /// 是否启用页范围视频删除
        /// </summary>
        public bool EnableSlideRangeVideoDeletion { get; set; } = false;

        /// <summary>
        /// 视频删除页范围开始页
        /// </summary>
        public int VideoSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 视频删除页范围结束页
        /// </summary>
        public int VideoSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页视频
        /// </summary>
        public bool DeleteLastSlideVideo { get; set; } = false;
    }

    /// <summary>
    /// 联系方式删除设置
    /// </summary>
    public class ContactDeletionSettings
    {
        /// <summary>
        /// 是否删除手机号码
        /// </summary>
        public bool DeletePhoneNumbers { get; set; } = false;

        /// <summary>
        /// 是否删除固定电话号码
        /// </summary>
        public bool DeleteLandlineNumbers { get; set; } = false;

        /// <summary>
        /// 是否删除电子邮箱
        /// </summary>
        public bool DeleteEmailAddresses { get; set; } = false;

        /// <summary>
        /// 是否删除网址
        /// </summary>
        public bool DeleteWebsites { get; set; } = false;

        /// <summary>
        /// 是否删除超链接
        /// </summary>
        public bool DeleteHyperlinks { get; set; } = false;
    }

    /// <summary>
    /// 动画删除设置
    /// </summary>
    public class AnimationDeletionSettings
    {
        /// <summary>
        /// 是否删除所有动画效果
        /// </summary>
        public bool DeleteAllAnimations { get; set; } = false;

        /// <summary>
        /// 是否启用页范围动画删除
        /// </summary>
        public bool EnableSlideRangeAnimationDeletion { get; set; } = false;

        /// <summary>
        /// 动画删除页范围开始页
        /// </summary>
        public int AnimationSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 动画删除页范围结束页
        /// </summary>
        public int AnimationSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页动画
        /// </summary>
        public bool DeleteLastSlideAnimations { get; set; } = false;

        /// <summary>
        /// 是否删除所有切换效果
        /// </summary>
        public bool DeleteAllTransitions { get; set; } = false;

        /// <summary>
        /// 是否启用页范围切换效果删除
        /// </summary>
        public bool EnableSlideRangeTransitionDeletion { get; set; } = false;

        /// <summary>
        /// 切换效果删除页范围开始页
        /// </summary>
        public int TransitionSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 切换效果删除页范围结束页
        /// </summary>
        public int TransitionSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页切换效果
        /// </summary>
        public bool DeleteLastSlideTransitions { get; set; } = false;
    }

    /// <summary>
    /// 备注删除设置
    /// </summary>
    public class NotesDeletionSettings
    {
        /// <summary>
        /// 是否删除幻灯片备注
        /// </summary>
        public bool DeleteSlideNotes { get; set; } = false;

        /// <summary>
        /// 是否清空备注内容
        /// </summary>
        public bool ClearNotesContent { get; set; } = false;
    }

    /// <summary>
    /// 格式删除设置
    /// </summary>
    public class FormatDeletionSettings
    {
        /// <summary>
        /// 是否删除字体格式
        /// </summary>
        public bool DeleteFontFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除段落格式
        /// </summary>
        public bool DeleteParagraphFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除表格格式
        /// </summary>
        public bool DeleteTableFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除列表格式
        /// </summary>
        public bool DeleteListFormatting { get; set; } = false;
    }


}
