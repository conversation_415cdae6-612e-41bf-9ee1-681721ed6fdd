using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容替换设置窗体 - 事件处理
    /// </summary>
    public partial class ContentReplacementForm
    {
        #region 文本替换规则事件

        /// <summary>
        /// 添加文本替换规则
        /// </summary>
        private void BtnAddTextReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new TextReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.TextReplacement.ReplacementRules.Add(ruleForm.Rule);
                    LoadTextReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑文本替换规则
        /// </summary>
        private void BtnEditTextReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("TextReplacement", "listViewTextReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is TextReplacementRule rule)
                    {
                        using var ruleForm = new TextReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadTextReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除文本替换规则
        /// </summary>
        private void BtnDeleteTextReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("TextReplacement", "listViewTextReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var result = MessageBox.Show("确定要删除选中的规则吗？", "确认删除",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        var selectedItem = listView.SelectedItems[0];
                        if (selectedItem.Tag is TextReplacementRule rule)
                        {
                            _currentSettings.TextReplacement.ReplacementRules.Remove(rule);
                            LoadTextReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入文本替换规则
        /// </summary>
        private void BtnImportTextReplacementRules_Click(object? sender, EventArgs e)
        {
            try
            {
                using var openFileDialog = new OpenFileDialog
                {
                    Title = "导入文本替换规则",
                    Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "json"
                };

                if (openFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var jsonContent = File.ReadAllText(openFileDialog.FileName);
                    var importedRules = JsonSerializer.Deserialize<TextReplacementRule[]>(jsonContent);

                    if (importedRules != null && importedRules.Length > 0)
                    {
                        var result = MessageBox.Show($"找到 {importedRules.Length} 条规则，是否导入？", "确认导入",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (result == DialogResult.Yes)
                        {
                            foreach (var rule in importedRules)
                            {
                                _currentSettings.TextReplacement.ReplacementRules.Add(rule);
                            }
                            LoadTextReplacementRules();
                            MessageBox.Show($"成功导入 {importedRules.Length} 条规则", "导入成功",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    else
                    {
                        MessageBox.Show("文件中没有找到有效的规则", "导入失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导出文本替换规则
        /// </summary>
        private void BtnExportTextReplacementRules_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_currentSettings.TextReplacement.ReplacementRules.Count == 0)
                {
                    MessageBox.Show("没有可导出的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出文本替换规则",
                    Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "json",
                    FileName = $"文本替换规则_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var jsonContent = JsonSerializer.Serialize(_currentSettings.TextReplacement.ReplacementRules.ToArray(),
                        new JsonSerializerOptions { WriteIndented = true, Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping });

                    File.WriteAllText(saveFileDialog.FileName, jsonContent);
                    MessageBox.Show($"成功导出 {_currentSettings.TextReplacement.ReplacementRules.Count} 条规则", "导出成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 形状替换规则事件

        /// <summary>
        /// 添加图片替换规则
        /// </summary>
        private void BtnAddImageReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("图片替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑图片替换规则
        /// </summary>
        private void BtnEditImageReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("图片替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除图片替换规则
        /// </summary>
        private void BtnDeleteImageReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("图片替换规则删除功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 添加文本框替换规则
        /// </summary>
        private void BtnAddTextBoxReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("文本框替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑文本框替换规则
        /// </summary>
        private void BtnEditTextBoxReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("文本框替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除文本框替换规则
        /// </summary>
        private void BtnDeleteTextBoxReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("文本框替换规则删除功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 添加形状样式替换规则
        /// </summary>
        private void BtnAddShapeStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("形状样式替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑形状样式替换规则
        /// </summary>
        private void BtnEditShapeStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("形状样式替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除形状样式替换规则
        /// </summary>
        private void BtnDeleteShapeStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("形状样式替换规则删除功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region 字体替换规则事件

        /// <summary>
        /// 添加字体名称替换规则
        /// </summary>
        private void BtnAddFontNameReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("字体名称替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑字体名称替换规则
        /// </summary>
        private void BtnEditFontNameReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("字体名称替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除字体名称替换规则
        /// </summary>
        private void BtnDeleteFontNameReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("字体名称替换规则删除功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 添加字体样式替换规则
        /// </summary>
        private void BtnAddFontStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("字体样式替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑字体样式替换规则
        /// </summary>
        private void BtnEditFontStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("字体样式替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除字体样式替换规则
        /// </summary>
        private void BtnDeleteFontStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("字体样式替换规则删除功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 添加字体到嵌入列表
        /// </summary>
        private void BtnAddFontToEmbed_Click(object? sender, EventArgs e)
        {
            try
            {
                // 使用简单的输入对话框
                string fontName = ShowInputDialog("请输入要嵌入的字体名称:", "添加字体");
                if (!string.IsNullOrWhiteSpace(fontName))
                {
                    var listBox = FindControlInPanel<ListBox>("FontReplacement", "listBoxFontsToEmbed");
                    if (listBox != null && !listBox.Items.Contains(fontName))
                    {
                        listBox.Items.Add(fontName);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加字体失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示输入对话框
        /// </summary>
        private string ShowInputDialog(string prompt, string title)
        {
            Form inputForm = new Form()
            {
                Width = 400,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = title,
                StartPosition = FormStartPosition.CenterParent,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label textLabel = new Label() { Left = 20, Top = 20, Width = 350, Text = prompt };
            TextBox textBox = new TextBox() { Left = 20, Top = 45, Width = 350 };
            Button confirmation = new Button() { Text = "确定", Left = 220, Width = 75, Top = 75, DialogResult = DialogResult.OK };
            Button cancel = new Button() { Text = "取消", Left = 300, Width = 75, Top = 75, DialogResult = DialogResult.Cancel };

            confirmation.Click += (sender, e) => { inputForm.Close(); };
            cancel.Click += (sender, e) => { inputForm.Close(); };

            inputForm.Controls.Add(textLabel);
            inputForm.Controls.Add(textBox);
            inputForm.Controls.Add(confirmation);
            inputForm.Controls.Add(cancel);
            inputForm.AcceptButton = confirmation;
            inputForm.CancelButton = cancel;

            return inputForm.ShowDialog(this) == DialogResult.OK ? textBox.Text : "";
        }

        /// <summary>
        /// 从嵌入列表移除字体
        /// </summary>
        private void BtnRemoveFontToEmbed_Click(object? sender, EventArgs e)
        {
            try
            {
                var listBox = FindControlInPanel<ListBox>("FontReplacement", "listBoxFontsToEmbed");
                if (listBox?.SelectedItem != null)
                {
                    listBox.Items.Remove(listBox.SelectedItem);
                }
                else
                {
                    MessageBox.Show("请选择要移除的字体", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"移除字体失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 颜色替换规则事件

        /// <summary>
        /// 添加主题颜色替换规则
        /// </summary>
        private void BtnAddThemeColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("主题颜色替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑主题颜色替换规则
        /// </summary>
        private void BtnEditThemeColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("主题颜色替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除主题颜色替换规则
        /// </summary>
        private void BtnDeleteThemeColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("主题颜色替换规则删除功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 添加自定义颜色替换规则
        /// </summary>
        private void BtnAddCustomColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("自定义颜色替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑自定义颜色替换规则
        /// </summary>
        private void BtnEditCustomColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("自定义颜色替换规则编辑功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除自定义颜色替换规则
        /// </summary>
        private void BtnDeleteCustomColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("自定义颜色替换规则删除功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }
}
