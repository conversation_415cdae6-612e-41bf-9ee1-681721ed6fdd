using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容替换设置窗体辅助方法
    /// </summary>
    public partial class ContentReplacementForm
    {
        #region UI创建辅助方法

        /// <summary>
        /// 创建可滚动的面板
        /// </summary>
        private Panel CreateScrollablePanel()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(5)
            };
        }

        /// <summary>
        /// 创建复选框
        /// </summary>
        private CheckBox CreateCheckBox(string text, int x, int y)
        {
            return new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(350, 23),
                UseVisualStyleBackColor = true,
                AutoSize = false
            };
        }

        /// <summary>
        /// 创建分组框
        /// </summary>
        private GroupBox CreateGroupBox(string text, int x, int y, int width, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
        }

        /// <summary>
        /// 创建按钮
        /// </summary>
        private Button CreateButton(string text, int x, int y, int width = 75, int height = 30)
        {
            return new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                UseVisualStyleBackColor = true,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
        }

        /// <summary>
        /// 创建标签
        /// </summary>
        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
        }

        /// <summary>
        /// 创建下拉框
        /// </summary>
        private ComboBox CreateComboBox(int x, int y, int width)
        {
            return new ComboBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
        }

        /// <summary>
        /// 创建文本框
        /// </summary>
        private TextBox CreateTextBox(int x, int y, int width, bool centerAlign = false)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 23),
                TextAlign = centerAlign ? HorizontalAlignment.Center : HorizontalAlignment.Left,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // 确定按钮点击事件
                btnOK.Click += BtnOK_Click;

                // 取消按钮点击事件
                btnCancel.Click += BtnCancel_Click;

                // 应用按钮点击事件
                btnApply.Click += BtnApply_Click;

                // 重置按钮点击事件
                btnReset.Click += BtnReset_Click;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置事件处理程序失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToSettings();
                SaveSettingsToConfig();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToSettings();
                SaveSettingsToConfig();
                MessageBox.Show("设置已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要重置所有设置为默认值吗？", "确认重置",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _currentSettings = new ContentReplacementSettings();
                    LoadSettingsToUI();
                    MessageBox.Show("设置已重置为默认值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 设置加载和保存

        /// <summary>
        /// 加载设置到界面
        /// </summary>
        private void LoadSettingsToUI()
        {
            try
            {
                // 加载文本替换设置
                LoadTextReplacementSettings();

                // 加载形状替换设置
                LoadShapeReplacementSettings();

                // 加载字体替换设置
                LoadFontReplacementSettings();

                // 加载颜色替换设置
                LoadColorReplacementSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置到界面失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存界面设置到配置
        /// </summary>
        private void SaveUIToSettings()
        {
            try
            {
                // 保存文本替换设置
                SaveTextReplacementSettings();

                // 保存形状替换设置
                SaveShapeReplacementSettings();

                // 保存字体替换设置
                SaveFontReplacementSettings();

                // 保存颜色替换设置
                SaveColorReplacementSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存设置到配置文件
        /// </summary>
        private void SaveSettingsToConfig()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                config.ContentReplacementSettings = _currentSettings;
                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 查找控件辅助方法

        /// <summary>
        /// 在指定面板中查找控件
        /// </summary>
        private T? FindControlInPanel<T>(string panelName, string controlName) where T : Control
        {
            if (_tabPanels.TryGetValue(panelName, out Panel? panel))
            {
                return FindControlRecursive<T>(panel, controlName);
            }
            return null;
        }

        /// <summary>
        /// 递归查找控件
        /// </summary>
        private T? FindControlRecursive<T>(Control parent, string controlName) where T : Control
        {
            foreach (Control control in parent.Controls)
            {
                if (control.Name == controlName && control is T)
                {
                    return (T)control;
                }

                var found = FindControlRecursive<T>(control, controlName);
                if (found != null)
                {
                    return found;
                }
            }
            return null;
        }

        #endregion
    }
}
